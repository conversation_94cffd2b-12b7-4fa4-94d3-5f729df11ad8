<script lang="ts">
  import type { Message } from '../../types/index.js';

  export let message: Message;
  export let isSelected: boolean = false;
  export let onToggleDetails: () => void;

  function formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }

  function getSentimentColor(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return 'text-green-600 dark:text-green-400';
      case 'negative': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getEngagementColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getMotivationColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }
</script>

<div 
  class="flex flex-col space-y-2 p-4 rounded-lg {message.sender === 'therapist' ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500' : 'bg-gray-50 dark:bg-gray-900/20 border-l-4 border-gray-500'}"
  class:ring-2={isSelected}
  class:ring-primary-500={isSelected}
>
  <!-- Message Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-2">
      <span class="font-medium {message.sender === 'therapist' ? 'text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300'}">
        {message.sender === 'therapist' ? '👨‍⚕️ Therapist' : '👤 Patient'}
      </span>
      <span class="text-xs text-neutral-500 dark:text-neutral-400">
        {formatTimestamp(message.timestamp)}
      </span>
    </div>
    <button
      on:click={onToggleDetails}
      class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
    >
      {isSelected ? 'Hide Details' : 'Show Details'}
    </button>
  </div>

  <!-- Message Content -->
  <div class="text-neutral-900 dark:text-neutral-100">
    {message.content}
  </div>

  <!-- Therapeutic Analysis (for therapist messages) -->
  {#if message.sender === 'therapist' && message.metadata}
    <div class="mt-3 p-3 bg-white dark:bg-neutral-800 rounded border border-neutral-200 dark:border-neutral-700">
      <div class="text-xs font-bold text-blue-600 dark:text-blue-400 mb-2">
        Therapeutic Framework:
      </div>
      
      {#if message.metadata.therapeuticApproach}
        <div class="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span class="font-medium text-neutral-700 dark:text-neutral-300">Approach:</span>
            <span class="ml-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded">
              {message.metadata.therapeuticApproach.approachName}
            </span>
          </div>
          <div>
            <span class="font-medium text-neutral-700 dark:text-neutral-300">Technique:</span>
            <span class="ml-1 px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded">
              {message.metadata.therapeuticApproach.selectedTechnique?.name || 'N/A'}
            </span>
          </div>
        </div>
      {/if}

      {#if message.metadata.patientAnalysis}
        <div class="mt-3">
          <div class="text-xs font-bold text-green-600 dark:text-green-400 mb-2">
            Patient Analysis:
          </div>
          <div class="grid grid-cols-3 gap-2 text-xs">
            <div class="text-center">
              <div class="font-medium text-neutral-700 dark:text-neutral-300">Sentiment</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium uppercase {getSentimentColor(message.metadata.patientAnalysis.sentiment)} bg-white dark:bg-neutral-700">
                {message.metadata.patientAnalysis.sentiment}
              </span>
            </div>
            <div class="text-center">
              <div class="font-medium text-neutral-700 dark:text-neutral-300">Motivation</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium uppercase {getMotivationColor(message.metadata.patientAnalysis.motivationLevel)} bg-white dark:bg-neutral-700">
                {message.metadata.patientAnalysis.motivationLevel}
              </span>
            </div>
            <div class="text-center">
              <div class="font-medium text-neutral-700 dark:text-neutral-300">Engagement</div>
              <span class="px-2 py-1 rounded-full text-xs font-medium uppercase {getEngagementColor(message.metadata.patientAnalysis.engagementLevel)} bg-white dark:bg-neutral-700">
                {message.metadata.patientAnalysis.engagementLevel}
              </span>
            </div>
          </div>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Detailed Analysis (expandable) -->
  {#if isSelected && message.metadata}
    <div class="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
      <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
        Detailed Analysis
      </h4>
      
      {#if message.thinking}
        <div class="mb-3">
          <span class="text-xs font-medium text-yellow-700 dark:text-yellow-300">AI Thinking:</span>
          <p class="text-xs text-yellow-700 dark:text-yellow-300 mt-1 italic">
            {message.thinking}
          </p>
        </div>
      {/if}

      {#if message.metadata.confidence}
        <div class="mb-2">
          <span class="text-xs font-medium text-yellow-700 dark:text-yellow-300">
            Confidence: {Math.round(message.metadata.confidence * 100)}%
          </span>
        </div>
      {/if}

      {#if message.metadata.processingTime}
        <div>
          <span class="text-xs font-medium text-yellow-700 dark:text-yellow-300">
            Processing Time: {message.metadata.processingTime}ms
          </span>
        </div>
      {/if}
    </div>
  {/if}
</div>
