<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import ThemeToggle from '$components/ui/ThemeToggle.svelte';
  import ChatMessage from '$components/chat/ChatMessage.svelte';
  import TherapeuticAnalysisPanel from '$components/chat/TherapeuticAnalysisPanel.svelte';
  import EvaluationMetricsDashboard from '$components/chat/EvaluationMetricsDashboard.svelte';
  import ConversationFilters from '$components/chat/ConversationFilters.svelte';
  import ConversationList from '$components/chat/ConversationList.svelte';
  import type {
    Conversation
  } from '../../types/index.js';

  // State management
  let conversations: Conversation[] = [];
  let selectedConversation: Conversation | null = null;
  let loading = true;
  let error: string | null = null;
  
  // Filtering and search
  let searchQuery = '';
  let selectedPersonaFilter = '';
  let selectedTherapistMode = '';
  let selectedDateRange = '';
  let showFilters = false;
  
  // Available filter options
  let availablePersonas: string[] = [];

  // Pagination
  let currentPage = 1;
  let itemsPerPage = 10;
  let totalConversations = 0;

  // UI state
  let activeTab = 'overview'; // overview, analysis, evaluation
  let selectedMessageId: string | null = null;

  onMount(async () => {
    await loadConversations();
    await loadFilterOptions();
  });

  async function loadConversations() {
    try {
      loading = true;
      error = null;
      
      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: ((currentPage - 1) * itemsPerPage).toString(),
        orderBy: 'created_at',
        orderDirection: 'desc'
      });

      // Apply filters
      if (selectedPersonaFilter) params.append('patientPersonaId', selectedPersonaFilter);
      if (selectedTherapistMode) params.append('therapistMode', selectedTherapistMode);
      if (selectedDateRange && selectedDateRange !== 'all') {
        const { start, end } = getDateRange(selectedDateRange);
        params.append('startDate', start);
        params.append('endDate', end);
      }

      const response = await fetch(`http://localhost:3000/api/conversations?${params}`);
      if (!response.ok) {
        throw new Error(`Failed to load conversations: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch conversations');
      }

      conversations = result.data.conversations || [];
      totalConversations = result.data.total || conversations.length;
      
      // Auto-select first conversation if none selected
      if (conversations.length > 0 && !selectedConversation) {
        await selectConversation(conversations[0].id);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load conversations';
      console.error('Error loading conversations:', err);
    } finally {
      loading = false;
    }
  }

  async function loadFilterOptions() {
    try {
      // Load available personas
      const personasResponse = await fetch('http://localhost:3000/api/personas');
      if (personasResponse.ok) {
        const personasData = await personasResponse.json();
        availablePersonas = personasData.personas?.map((p: any) => p.id) || [];
      }
    } catch (err) {
      console.error('Error loading filter options:', err);
    }
  }

  async function selectConversation(conversationId: string) {
    try {
      const response = await fetch(`http://localhost:3000/api/conversations/${conversationId}/complete`);
      if (!response.ok) {
        throw new Error(`Failed to load conversation: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch conversation details');
      }

      // Map the complete conversation data to our frontend format
      const completeData = result.data;
      selectedConversation = {
        ...completeData.conversation,
        messages: completeData.messages || [],
        analytics: completeData.sessionAnalytics,
        startTime: completeData.conversation.created_at,
        endTime: completeData.conversation.updated_at,
        config: completeData.conversation.config || {},
        thoughts: []
      };
      
      // Mark as selected in the list
      conversations = conversations.map(conv => ({
        ...conv,
        selected: conv.id === conversationId
      }));
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load conversation details';
      console.error('Error loading conversation:', err);
    }
  }

  function getDateRange(range: string): { start: string; end: string } {
    const now = new Date();
    const end = now.toISOString();
    let start: Date;

    switch (range) {
      case 'today':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case '7days':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(0);
    }

    return { start: start.toISOString(), end };
  }

  // Reactive statements for filtering
  $: filteredConversations = conversations.filter(conv => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return conv.id.toLowerCase().includes(query) ||
             conv.session_metadata?.patientPersonaId?.toLowerCase().includes(query);
    }
    return true;
  });

  // Reactive statements for pagination
  $: totalPages = Math.ceil(totalConversations / itemsPerPage);

  // Handle filter changes
  $: if (selectedPersonaFilter || selectedTherapistMode || selectedDateRange) {
    currentPage = 1;
    loadConversations();
  }

  function clearFilters() {
    selectedPersonaFilter = '';
    selectedTherapistMode = '';
    selectedDateRange = '';
    searchQuery = '';
    currentPage = 1;
    loadConversations();
  }

  function goToLiveSession() {
    goto('/');
  }
</script>

<svelte:head>
  <title>Chat History - MiCA Therapy</title>
</svelte:head>

<div class="min-h-screen bg-white dark:bg-neutral-900 transition-colors duration-200">
  <!-- Header -->
  <header class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          on:click={goToLiveSession}
          class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
        >
          ← Back to Live Session
        </button>
        <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
          Chat History & Analysis
        </h1>
      </div>
      <div class="flex items-center space-x-4">
        <button
          on:click={() => showFilters = !showFilters}
          class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          {showFilters ? 'Hide' : 'Show'} Filters
        </button>
        <ThemeToggle />
      </div>
    </div>
  </header>

  <div class="flex flex-col lg:flex-row h-[calc(100vh-80px)]">
    <!-- Sidebar - Conversation List -->
    <div class="w-full lg:w-1/3 border-r border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 flex flex-col {selectedConversation ? 'hidden lg:flex' : 'flex'}">
      <!-- Filters Panel -->
      {#if showFilters}
        <ConversationFilters
          bind:searchQuery
          bind:selectedPersonaFilter
          bind:selectedTherapistMode
          bind:selectedDateRange
          {availablePersonas}
          on:filterChange={loadConversations}
          on:clearFilters={clearFilters}
          on:export={() => console.log('Export functionality to be implemented')}
        />
      {/if}

      <!-- Conversation List -->
      <ConversationList
        conversations={filteredConversations}
        selectedConversationId={selectedConversation?.id || null}
        {loading}
        {error}
        {currentPage}
        {totalPages}
        {totalConversations}
        on:select={(e) => selectConversation(e.detail.conversationId)}
        on:pageChange={(e) => {
          currentPage = e.detail.page;
          loadConversations();
        }}
        on:retry={loadConversations}
      />
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col {selectedConversation ? 'flex' : 'hidden lg:flex'}">
      {#if selectedConversation}
        <!-- Conversation Header -->
        <div class="p-4 lg:p-6 border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <!-- Mobile Back Button -->
              <button
                on:click={() => selectedConversation = null}
                class="lg:hidden p-2 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                aria-label="Back to conversation list"
              >
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <div>
                <h2 class="text-lg lg:text-xl font-bold text-neutral-900 dark:text-neutral-100">
                  Session {selectedConversation.id.slice(-8)}
                </h2>
              </div>
              <div class="flex items-center space-x-4 mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                <span>
                  <span class="font-medium">Persona:</span>
                  {selectedConversation.session_metadata?.patientPersonaId || 'Unknown'}
                </span>
                <span>
                  <span class="font-medium">Mode:</span>
                  {selectedConversation.therapist_mode === 'multi-therapist' ? 'Multi-Therapist' : 'Single Therapist'}
                </span>
                <span>
                  <span class="font-medium">Duration:</span>
                  {selectedConversation.endTime ?
                    Math.round((new Date(selectedConversation.endTime).getTime() - new Date(selectedConversation.startTime).getTime()) / 60000) + ' min' :
                    'Ongoing'
                  }
                </span>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <span class="px-3 py-1 text-sm rounded-full {selectedConversation.status === 'completed' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' : selectedConversation.status === 'active' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300'}">
                {selectedConversation.status}
              </span>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800">
          <nav class="flex space-x-8 px-6">
            <button
              class="py-4 px-1 border-b-2 font-medium text-sm {activeTab === 'overview' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300'}"
              on:click={() => activeTab = 'overview'}
            >
              Chat History
            </button>
            <button
              class="py-4 px-1 border-b-2 font-medium text-sm {activeTab === 'analysis' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300'}"
              on:click={() => activeTab = 'analysis'}
            >
              Therapeutic Analysis
            </button>
            <button
              class="py-4 px-1 border-b-2 font-medium text-sm {activeTab === 'evaluation' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300'}"
              on:click={() => activeTab = 'evaluation'}
            >
              Evaluation Metrics
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="flex-1 overflow-y-auto bg-neutral-50 dark:bg-neutral-900">
          {#if activeTab === 'overview'}
            <!-- Chat History Tab -->
            <div class="p-6">
              {#if selectedConversation.messages && selectedConversation.messages.length > 0}
                <div class="space-y-4">
                  {#each selectedConversation.messages as message}
                    <ChatMessage
                      {message}
                      isSelected={selectedMessageId === message.id}
                      onToggleDetails={() => selectedMessageId = selectedMessageId === message.id ? null : message.id}
                    />
                  {/each}
                </div>
              {:else}
                <div class="text-center py-8">
                  <p class="text-neutral-600 dark:text-neutral-400">No messages in this conversation</p>
                </div>
              {/if}
            </div>
          {:else if activeTab === 'analysis'}
            <!-- Therapeutic Analysis Tab -->
            <div class="p-6">
              <TherapeuticAnalysisPanel conversation={selectedConversation} />
            </div>
          {:else if activeTab === 'evaluation'}
            <!-- Evaluation Metrics Tab -->
            <div class="p-6">
              <EvaluationMetricsDashboard conversation={selectedConversation} />
            </div>
          {/if}
        </div>

        <!-- Tab Content -->
        <div class="flex-1 overflow-y-auto">
          {#if activeTab === 'overview'}
            <!-- Chat History Tab -->
            <div class="p-6">
              {#if selectedConversation.messages && selectedConversation.messages.length > 0}
                <div class="space-y-4">
                  {#each selectedConversation.messages as message}
                    <ChatMessage
                      {message}
                      isSelected={selectedMessageId === message.id}
                      onToggleDetails={() => selectedMessageId = selectedMessageId === message.id ? null : message.id}
                    />
                  {/each}
                </div>
              {:else}
                <div class="text-center py-8">
                  <p class="text-neutral-600 dark:text-neutral-400">No messages in this conversation</p>
                </div>
              {/if}
            </div>
          {:else if activeTab === 'analysis'}
            <!-- Therapeutic Analysis Tab -->
            <div class="p-6">
              <TherapeuticAnalysisPanel conversation={selectedConversation} />
            </div>
          {:else if activeTab === 'evaluation'}
            <!-- Evaluation Metrics Tab -->
            <div class="p-6">
              <EvaluationMetricsDashboard conversation={selectedConversation} />
            </div>
          {/if}
        </div>
      {:else}
        <!-- No Conversation Selected -->
        <div class="flex-1 flex items-center justify-center bg-neutral-50 dark:bg-neutral-900">
          <div class="text-center">
            <div class="text-neutral-400 dark:text-neutral-500 text-6xl mb-4">💬</div>
            <h3 class="text-xl font-medium text-neutral-600 dark:text-neutral-400 mb-2">
              Select a Conversation
            </h3>
            <p class="text-neutral-500 dark:text-neutral-500">
              Choose a conversation from the sidebar to view its details and analysis.
            </p>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>
